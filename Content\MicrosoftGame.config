<?xml version="1.0" encoding="utf-8"?>
<Game configVersion="0">
   
    <Identity Name="BethesdaSoftworks.TheEvilWithin2PC" Publisher="CN=21E520D9-F467-4438-A16E-79ADDBE4ECB1" Version="1.9.0.0"/>
        
    <StoreId>9NT0NT8JQMQN</StoreId>
	<TitleId>6ABF4A28</TitleId>
	
    <ExecutableList>
      <Executable Name="TEW2.exe"
            	  Id="Game"
				  TargetDeviceFamily="PC"
           		  OverrideDisplayName="The Evil Within 2 (PC)"/>
    </ExecutableList>

    <ShellVisuals DefaultDisplayName="The Evil Within 2 (PC)" 
                  PublisherDisplayName="Bethesda Softworks"
                  StoreLogo="TEW2_storelogo.png"
                  Square150x150Logo="TEW2_logo.png"
                  Square44x44Logo="TEW2_smalllogo.png"
                  BackgroundColor="#000040"
                  SplashScreenImage="TEW2_splashscreen.png"/>
    	  	
	<Resources>
		<Resource Language="en-US"/>
		<Resource Language="fr-FR"/>
		<Resource Language="de-DE"/>
		<Resource Language="es-ES"/>
		<Resource Language="es-419"/>
		<Resource Language="it-IT"/>
		<Resource Language="pl-PL"/>
		<Resource Language="pt-BR"/>
		<Resource Language="ru-RU"/>
		<Resource Language="ja-JP"/>
		<Resource Language="ko-KR"/>
		<Resource Language="zh-CN"/>
		<Resource Language="zh-SG"/>
	</Resources>

  <DesktopRegistration>

    <DependencyList>
      <Dependency Name="Microsoft.DirectXRuntime" MinVersion="9.29.952.0"/>
      <Dependency Name="Microsoft.VCLibs.140.00.UWPDesktop" MinVersion="14.0.24217.0"/>        
    </DependencyList>
	
    <ProcessorArchitecture>x64</ProcessorArchitecture>
	
    <EnableWritesToPackageRoot>true</EnableWritesToPackageRoot>
	
  </DesktopRegistration>
  
</Game>
