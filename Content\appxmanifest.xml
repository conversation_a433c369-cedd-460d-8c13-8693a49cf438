<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10" xmlns:desktop6="http://schemas.microsoft.com/appx/manifest/desktop/windows10/6" xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10" xmlns:uap3="http://schemas.microsoft.com/appx/manifest/uap/windows10/3" xmlns:wincap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/windowscapabilities" xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities" IgnorableNamespaces="uap uap3 desktop desktop6 wincap rescap" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
  <Identity Name="BethesdaSoftworks.TheEvilWithin2PC" Publisher="CN=21E520D9-F467-4438-A16E-79ADDBE4ECB1" Version="*******" ProcessorArchitecture="x64" />
  <Properties>
    <DisplayName>The Evil Within 2 (PC)</DisplayName>
    <PublisherDisplayName>Bethesda Softworks</PublisherDisplayName>
    <Logo>TEW2_storelogo.png</Logo>
    <Description>BethesdaSoftworks.TheEvilWithin2PC</Description>
    <desktop6:RegistryWriteVirtualization>disabled</desktop6:RegistryWriteVirtualization>
    <desktop6:FileSystemWriteVirtualization>disabled</desktop6:FileSystemWriteVirtualization>
  </Properties>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.18362.0" MaxVersionTested="10.0.18362.0" />
    <PackageDependency Name="Microsoft.DirectXRuntime" MinVersion="9.29.952.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
    <PackageDependency Name="Microsoft.VCLibs.140.00.UWPDesktop" MinVersion="14.0.24217.0" Publisher="CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US" />
  </Dependencies>
  <Resources>
    <Resource Language="en-US" />
    <Resource Language="fr-FR" />
    <Resource Language="de-DE" />
    <Resource Language="es-ES" />
    <Resource Language="es-419" />
    <Resource Language="it-IT" />
    <Resource Language="pl-PL" />
    <Resource Language="pt-BR" />
    <Resource Language="ru-RU" />
    <Resource Language="ja-JP" />
    <Resource Language="ko-KR" />
    <Resource Language="zh-CN" />
    <Resource Language="zh-SG" />
  </Resources>
  <Applications>
    <Application Id="Game" Executable="GameLaunchHelper.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements DisplayName="The Evil Within 2 (PC)" Square150x150Logo="TEW2_logo.png" Square44x44Logo="TEW2_smalllogo.png" Description="BethesdaSoftworks.TheEvilWithin2PC" ForegroundText="light" BackgroundColor="#000040">
        <uap:SplashScreen Image="TEW2_splashscreen.png" />
      </uap:VisualElements>
      <Extensions>
        <uap:Extension Category="windows.protocol">
          <uap:Protocol Name="ms-xbl-6abf4a28" />
        </uap:Extension>
      </Extensions>
    </Application>
  </Applications>
  <Capabilities>
    <Capability Name="internetClient" />
    <rescap:Capability Name="runFullTrust" />
    <rescap:Capability Name="appLicensing" />
    <rescap:Capability Name="unvirtualizedResources" />
  </Capabilities>
</Package>