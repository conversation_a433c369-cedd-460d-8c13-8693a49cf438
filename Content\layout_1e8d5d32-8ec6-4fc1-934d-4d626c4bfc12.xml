<Package>
  <Chunk Id="1000" Marker="Launch">
    <FileGroup DestinationPath="." SourcePath="." Include="*.*" />
    <FileGroup DestinationPath=".\base" SourcePath=".\base" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink" SourcePath=".\base\bink" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink\cutscene" SourcePath=".\base\bink\cutscene" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink\digest" SourcePath=".\base\bink\digest" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink\flashback" SourcePath=".\base\bink\flashback" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink\skydome" SourcePath=".\base\bink\skydome" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink\tvscreen" SourcePath=".\base\bink\tvscreen" Include="*.*" />
    <FileGroup DestinationPath=".\base\bink\underground" SourcePath=".\base\bink\underground" Include="*.*" />
    <FileGroup DestinationPath=".\base\ptr" SourcePath=".\base\ptr" Include="*.*" />
    <FileGroup DestinationPath=".\patch" SourcePath=".\patch" Include="*.*" />
    <FileGroup DestinationPath=".\patch\ptr" SourcePath=".\patch\ptr" Include="*.*" />
    <FileGroup DestinationPath=".\virtualtextures" SourcePath=".\virtualtextures" Include="*.*" />
  </Chunk>
</Package>
